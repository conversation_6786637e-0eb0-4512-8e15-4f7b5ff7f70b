<template>
  <view class="page-container">
    <page-meta :page-style="pageStyle" :page-scroll="true" :page-font-size="16"></page-meta>
    <!-- 添加遮罩层 -->
    <view class="mask" v-if="showPriceDetail" @click="closePriceDetail"></view>
    <cu-header-free :titleBarHeight="80" :titleBarBg="titleBarBg">
      <template #rightContainer>
        <view class="items">
          <view class="item">
            <!-- <up-icon name="share" size="32rpx"></up-icon>
            <text>分享</text> -->
          </view>
        </view>
      </template>
    </cu-header-free>
    <view class="container-bg" :style="{ background: containerBg }"></view>
    <view class="content-container">
      <view class="container-tab" :style="{ opacity: transparency2 }">
        <view class="custom-tabs">
          <view v-for="(item, index) in containerTabList" :key="index" class="tab-item" :class="{ active: current === index }">
            <text class="tab-text">{{ item.name }}</text>
            <view class="tab-line" v-if="current === index"></view>
          </view>
        </view>
      </view>
      <!-- <view class="goods-type-box">
        <view class="goods-type">
          <text>拼车</text>
          <view class="goods-type-line"></view>
          <view class="goods-type-tips">配货降空载·多拼多赚钱</view>
        </view>
      </view> -->
      <view class="route-info-box" ref="routeInfoBox" id="routeInfoBox">
        <view class="route-info">
          <view class="route-info-base">
            <up-icon name="car" size="38rpx" color="#eb524c"></up-icon>
            <text>{{ formatDate(orderDetail.loadingDate) }} {{orderDetail.earliestLoadingTime}}-{{orderDetail.latestLoadingTime}}装货，一装一卸</text>
          </view>
          <view class="address-box">
            <view class="loading-info loading-info-line">
              <view class="loading-info-left">
                <view class="icon-text">装</view>
                <view class="loading-info-left-address">
                  <view class="big-name">{{orderDetail.loadingAddressName}}</view>
                  <view class="small-name">{{orderDetail.loadingAddress.province}} {{orderDetail.loadingAddress.county}}</view>
                </view>
              </view>
            </view>
            <view class="loading-info unloading-info">
              <view class="loading-info-left">
                <view class="icon-text">卸</view>
                <view class="loading-info-left-address">
                  <view class="big-name">{{orderDetail.unloadingAddressName}}</view>
                  <view class="small-name">{{orderDetail.dischargeAddress.province}} {{orderDetail.dischargeAddress.county}}</view>
                </view>
              </view>
            </view>
          </view>
          <!-- <view class="car-info">
            <view class="car-info-left">
              <up-icon name="error-circle" size="30rpx" color="#999"></up-icon>
              <text class="car-info-left-text">补充车辆信息方可查看装卸货地限行情况。</text>
            </view>
            <up-icon name="arrow-right" size="26rpx" color="#999"></up-icon>
          </view> -->
          <view class="distance-info">
            <view class="distance-info-left">
              <view class="distance-info-left-type1">
                <text class="num-unit">
                  <text class="num">34.3</text>
                  <text class="unit">km</text>
                </text>
                <text class="label">距装货地约</text>
              </view>
              <view class="distance-info-left-type2" @click="goToLoadingLocation">
                <view class="iconfont icon-owner-dingwei2"></view>
                <text>去装货地</text>
              </view>
            </view>
            <div class="distance-info-line"></div>
            <view class="distance-info-left">
              <view class="distance-info-left-type1">
                <text class="num-unit">
                  <text class="num">34.3</text>
                  <text class="unit">km</text>
                </text>
                <text class="label">距装货地约</text>
              </view>
              <view class="distance-info-left-type2" @click="viewRouteInfo">
                <view class="iconfont icon-owner--_xianlu"></view>
                <text>查看路线</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="shipment-info-box" ref="shipmentInfoBox" id="shipmentInfoBox">
        <view class="shipment-info">
          <view class="shipment-info-title">装货信息</view>
          <cu-cell>
            <template #label>车辆</template>
            <template #showText>{{orderDetail.vehicleLength}}米 {{orderDetail.vehicleType}}</template>
          </cu-cell>
          <cu-cell>
            <template #label>货物</template>
            <template #showText>{{orderDetail.cargoName}}，{{orderDetail.maxWeight}}吨，{{orderDetail.packagingMethod}}</template>
          </cu-cell>
          <cu-cell>
            <template #label>备注</template>
            <template #showText>{{orderDetail.remarks}}</template>
          </cu-cell>
          <!-- <view class="shipment-info-waring">
            <up-icon name="info-circle" size="32rpx" color="#e64339"></up-icon>
            <text>1人跟车，请按照平台要求操作并注意保护您与跟车人员的安全</text>
          </view>
          <view class="notice-box">
            <up-notice-bar :text="noticeList" direction="column" icon="" bgColor="#fafafa" color="#737373"></up-notice-bar>
          </view> -->
        </view>
      </view>
      <view class="price-info-box">
        <view class="price-info">
          <view class="price-header">
            <view class="price-type">
              <up-icon name="info-circle" size="32rpx" color="#e84138"></up-icon>
              <text>一口价货源</text>
            </view>
            <!-- <text class="price-desc">点击立即抢单，在线支付订金后可联系货主</text> -->
          </view>

          <view class="price-content">
            <view class="price-main">
              <text class="price-label">预估净得运费</text>
              <view class="price-value">
                <text class="price-number">{{orderDetail.bidAmount}}</text>
                <text class="price-unit">元</text>
              </view>
              <text class="price-calc">¥ {{orderDetail.bidAmount}}(净得运费) = ¥ {{orderDetail.bidAmount}}(运费)</text>
            </view>

            <view class="price-details">
              <view class="price-item">
                <text class="item-label">总运费</text>
                <text class="item-value">{{orderDetail.bidAmount}}元/趟</text>
              </view>
              <view class="price-item">
                <text class="item-label">订金<text class="refund-tag">(退还②)</text></text>
                <text class="item-value">{{orderDetail.depositAmount}}元</text>
              </view>
              <!-- <view class="price-item">
                <text class="item-label">支付方式</text>
                <text class="item-value">到付</text>
              </view> -->
              <!-- <view class="price-item">
                <view class="item-label-box" @click="showServiceFeePopup">
                  <text>技术服务费</text>
                  <up-icon name="question-circle" size="24rpx" color="#999"></up-icon>
                </view>
                <view class="item-value">
                  <text class="original-price">5元</text>
                  <text class="current-price">4元</text>
                </view>
              </view> -->
              <!-- <view class="price-item discount">
                <text class="item-label">限时优惠</text>
                <text class="item-value">-1元</text>
              </view> -->
            </view>

            <!-- <view class="price-action">
              <up-button type="primary" text="申请加价" shape="circle"></up-button>
            </view> -->
          </view>
        </view>
      </view>

      <!-- 老板信息 -->
<!--      <view class="boss-info-box">
        <view class="boss-info">
          <view class="boss-left">
            <image class="avatar" src="/static/images/head.png" mode="aspectFill"></image>
            <view class="info">
              <view class="name-wrap">
                <text class="name">吴老板</text>
                &lt;!&ndash; <view class="credit">
                  <text class="credit-text">信用</text>
                  <up-rate v-model="bossCredit" readonly count="5" activeColor="#ff9500" size="20"></up-rate>
                </view> &ndash;&gt;
              </view>
              <view class="stats">
                <text>交易219</text>
                <text class="divider">|</text>
                <text>发货数354</text>
                &lt;!&ndash; <text class="divider">|</text>
                <text>好评率97%</text> &ndash;&gt;
              </view>
              <view class="certifications">
                <view class="cert-item">
                  <up-icon name="checkmark-circle" size="28rpx" color="#7ac251"></up-icon>
                  <text>实名认证</text>
                </view>
                <view class="cert-item">
                  <up-icon name="checkmark-circle" size="28rpx" color="#7ac251"></up-icon>
                  <text>企业认证</text>
                </view>
              </view>
            </view>
          </view>
          &lt;!&ndash; <view class="follow-btn">
            <up-button type="primary" text="+ 关注" plain size="small" shape="circle"></up-button>
          </view> &ndash;&gt;
        </view>
      </view>-->

      <!-- 推荐货源 -->
      <view class="recommend-box" ref="recommendBox" id="recommendBox">
        <view class="recommend-title">推荐货源</view>
        <view class="recommend-list">
          <view class="recommend-item" v-for="(item, index) in recommendList" :key="index">
            <view class="route">
              <view class="cities">
                <text class="from">{{ item.loadingAddressName }}</text>
                <text class="arrow">→</text>
                <text class="to">{{ item.unloadingAddressName }}</text>
              </view>
<!--
              <view class="tag" :class="item.type === '拼车' ? 'carpool' : 'fixed-price'">{{ item.type }}</view>
-->
            </view>
            <view class="goods-info">
              <text class="type">{{ item.vehicleType }}</text>
              <text class="time">{{ item.cargoName }} {{ item.packagingMethod }} {{ formatDate(item.loadingDate) }}
                {{ item.earliestLoadingTime }}-{{item.latestLoadingTime}}</text>
            </view>
            <view class="specs">
              <text>{{ item.vehicleLength }}</text>
              <text class="divider">|</text>
              <text>{{ item.vehicleType }}</text>
              <text class="divider">|</text>
              <text>{{ item.maxWeight }}</text>
              <text v-if="item.other" class="divider">|</text>
              <text v-if="item.other">{{ item.other }}</text>
            </view>
<!--            <view class="user-info">
              <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
              <view class="credit">信用 {{ item.credit }}星</view>
              <text class="deals">交易{{ item.deals }}</text>
              <text class="rate">好评率{{ item.rate }}</text>
            </view>-->
            <view class="price-action">
              <view class="price">
                <text class="amount">*****</text>
                <text class="unit">元/趟</text>
              </view>
              <text class="auth-tip">认证后查看运价</text>
              <up-button type="error" text="去抢单" size="mini" shape="circle" customStyle="width: 120rpx; height: 52rpx; font-size: 24rpx; padding: 0;"></up-button>
            </view>
          </view>
        </view>
      </view>
      <!-- 底部占位，防止内容被遮挡 -->
      <view class="bottom-placeholder"></view>
    </view>

    <!-- 底部固定操作栏 -->
    <view class="bottom-action-wrapper">
      <view class="bottom-action" :style="bottomActionStyle">
        <view class="price-info">
          <text class="amount">{{ orderDetail.bidAmount }}</text>
          <text class="unit">元</text>
          <text class="desc">净得运费</text>
          <text class="detail" @click="showPriceDetail = true">详情</text>
        </view>
        <view class="buttons">
          <!-- <up-button type="info" text="申请加价" plain shape="circle"></up-button> -->
          <up-button type="error" text="立即抢单" shape="circle" @click="driverGrabOrderInfo"></up-button>
        </view>
      </view>
    </view>

    <!-- 技术服务费说明弹窗 -->
    <up-popup :show="showServiceFee" @close="showServiceFee = false" mode="center" :round="10" :closeOnClickOverlay="true" backgroundColor="#fff" zIndex="99999">
      <view class="service-fee-popup">
        <view class="popup-header">
          <text class="title">技术服务费说明</text>
          <up-icon name="close" size="40rpx" color="#999" @click="showServiceFee = false"></up-icon>
        </view>

        <view class="popup-content">
          <view class="content-item">
            <view class="item-title">
              <up-icon name="checkmark-circle" size="36rpx" color="#e84138"></up-icon>
              <text>服务保障</text>
            </view>
            <text class="item-desc">包含但不限于货源信息匹配、电子协议、信息展示、在线支付、订单评价、客服等多项服务</text>
          </view>

          <view class="content-item">
            <view class="item-title">
              <up-icon name="file-text" size="36rpx" color="#e84138"></up-icon>
              <text>规则说明</text>
            </view>
            <text class="item-desc">技术服务费受市场环境、车货供需、成交时间、出发城市和运输路线等多因素综合影响</text>
          </view>

          <view class="content-item">
            <view class="item-title">
              <up-icon name="info-circle" size="36rpx" color="#e84138"></up-icon>
              <text>收费声明</text>
            </view>
            <text class="item-desc">平台按照公开、公平、公正的原则收取技术服务费，保障司机合法权益</text>
          </view>
        </view>

        <view class="popup-footer">
          <up-button type="primary" text="我知道了" @click="showServiceFee = false" shape="circle"></up-button>
        </view>
      </view>
    </up-popup>

    <!-- 结算详情弹窗 -->
    <up-popup :show="showPriceDetail" @close="closePriceDetail" mode="bottom" :round="20" :closeOnClickOverlay="true" backgroundColor="#fff" zIndex="99999" :safeAreaInsetBottom="true" :maskCloseAble="true" :overlayStyle="{ position: 'fixed' }">
      <view class="price-detail-popup">
        <view class="popup-header">
          <text class="title">详情</text>
          <up-icon name="close" size="40rpx" color="#999" @click="closePriceDetail"></up-icon>
        </view>

        <view class="popup-content">
          <view class="price-item main-price">
            <view class="label">预估净得运费</view>
            <view class="value">
              <text class="amount">{{ orderDetail.bidAmount }}</text>
              <text class="unit">元</text>
            </view>
            <view class="calc">¥ {{ orderDetail.bidAmount }}(净得运费)= ¥ {{ orderDetail.bidAmount }}(运费)</view>
          </view>

          <view class="price-item">
            <view class="label">总运费</view>
            <view class="value">
              <text class="amount">{{ orderDetail.bidAmount }}</text>
              <text class="unit">元/趟</text>
            </view>
          </view>

          <view class="price-item">
            <view class="label">
              订金
              <text class="tag">(退还)</text>
            </view>
            <view class="value">
              <text class="amount">{{ orderDetail.depositAmount }}</text>
              <text class="unit">元</text>
            </view>
          </view>

          <!-- <view class="price-item">
            <view class="label-box">
              <text>技术服务费</text>
              <view class="discount-tag">限时优惠</view>
            </view>
            <view class="value">
              <text class="original">5元</text>
              <text class="amount">4</text>
              <text class="unit">元</text>
              <text class="discount">-1元</text>
            </view>
          </view> -->

          <view class="tips">
            优惠券使用规则，详情请参考 我的-卡券包
          </view>
        </view>
      </view>
    </up-popup>
    
    <!-- 加价是否同意弹窗 -->
    <uni-popup ref="addPriceRef" background-color="#fff" class="global-popup price-add-popup" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
      <view class="popup-content">
        <view class="title-box">
          <text class="btn cancel">改价</text>
          <text class="btn confirm" @tap="closePrice">
            <u-icon name="close" size="18" color="#bbb" class="tag-close" />
          </text>
        </view>
        <view class="count-amount-box">
          <view class="count-amount">
            <text class="price">运费 |</text>
            <up-input class="price-input" :maxlength="7" type="number" placeholder="请输入运费金额" border="none" v-model="addPrice" disabled></up-input>
          </view>
          <view class="count-amount">
            <text class="price">定金 |</text>
            <up-input class="price-input" :maxlength="7" type="number" placeholder="请输入定金金额" border="none" v-model="deposit" disabled></up-input>
          </view>
        </view>
        <view class="confirm-btn">
          <button class="btn-com">同意</button>
          <button class="btn-com">拒绝</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch, onUnmounted, onMounted } from 'vue';
import { onLoad, onShow, onPageScroll } from '@dcloudio/uni-app';
import {getOrderInfoById, driverGrabOrders, allOrderInfoList} from "../../api/transport";
import dayjs from "dayjs";
const receivedId: any = ref('');
const orderDetail: any = ref({
  
});
onLoad((options) => {
  if (options.id) {
    receivedId.value = options.id;
    fetchOrderDetail(options.id);
  }
  fetchOrderList()
});

const addPriceRef = ref(null)
onMounted(() => {
  const addPrice = ref(10);//接口获取
  const deposit = ref(100)
  // const isUnAgree = ref(true)//接口获取
  // if(isUnAgree) {
  //   addPriceRef.value.open('bottom');
  // }
})
// 关闭改价
const closePrice = () => {
  addPriceRef.value.close();
}
// 获取订单列表
const fetchOrderList = async () => {
  try {
    const response: any = await allOrderInfoList({
    });
    uni.hideLoading();
    if (response.code === 700) {
        recommendList.value = response.result || [];
    }else {
      console.error('获取订单列表失败');
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
  }
};

// 获取订单详情的函数
const fetchOrderDetail = async (id: string) => {
  try {
    // 调用接口获取订单详情
    const response: any = await getOrderInfoById(id);
    uni.hideLoading();
    if (response.code === 700) {
      orderDetail.value = response.result;
      calculateDistance();
      console.log('fetchOrderDetail', orderDetail.value);
    } else {
      console.error('获取订单详情失败');
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
  }
};

const formatDate = (date: string | number | Date) => {
  if (!date) return '';
  return dayjs(date).format('YYYY-MM-DD');
};

const getOrderStatusText = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    0: '发货中',
    1: '运输中',
    2: '已完成',
    99: '已取消',
  };
  return statusMap[status] || '未知状态';
};
// 计算距离的函数
const estimatedDistance = ref(null);
const calculateDistance = () => {
  const startLng = orderDetail.value.loadingLongitude;
  const startLat = orderDetail.value.loadingLatitude;
  const endLng = orderDetail.value.unloadingLongitude;
  const endLat = orderDetail.value.unloadingLatitude;
  const R = 6371; // 地球半径，单位：公里
  const dLat = (endLat - startLat) * (Math.PI / 180);
  const dLng = (endLng - startLng) * (Math.PI / 180);
  const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(startLat * (Math.PI / 180)) *
      Math.cos(endLat * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  estimatedDistance.value = distance.toFixed(1);
};
const transparency = ref(1)
const transparency2 = ref(0)
const titleBarBg = computed(() => {
  return `linear-gradient(to bottom, rgba(122, 194, 81,${transparency.value}), rgba(122, 194, 81,${transparency.value}))`;
});
const containerBg = computed(() => {
  return `linear-gradient(to bottom, rgba(122, 194, 81,${transparency.value}), rgba(245, 245, 245,${transparency.value}))`;
})

// 获取系统信息和环境
const getSystemInfo = () => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    // #ifdef APP-PLUS
    return {
      safeAreaTop: sysInfo.safeAreaInsets?.top || 0,
      safeAreaBottom: sysInfo.safeAreaInsets?.bottom || 34,
      screenHeight: sysInfo.screenHeight || 0,
      statusBarHeight: sysInfo.statusBarHeight || 0,
      isApp: true
    };
    // #endif

    // #ifdef H5
    return {
      safeAreaTop: 0,
      safeAreaBottom: 0,
      screenHeight: sysInfo.screenHeight || 0,
      statusBarHeight: 0,
      isApp: false
    };
    // #endif
  } catch (e) {
    console.error('获取系统信息失败:', e);
    return {
      safeAreaTop: 0,
      safeAreaBottom: 0,
      screenHeight: 0,
      statusBarHeight: 0,
      isApp: false
    };
  }
};

const systemInfo = getSystemInfo();
const safeAreaBottom = computed(() => systemInfo.isApp ? systemInfo.safeAreaBottom : 0);
const heightHeader = computed(() => {
  return systemInfo.safeAreaTop * 2 + 80 + 'rpx';
});

// 底部操作栏高度
const bottomActionHeight = 120;

// 添加滚动相关状态
const scrollTop = ref(0)

const current = ref(0)
const containerTabList = reactive([
  { name: '装卸信息', value: 0 },
  { name: '装货信息', value: 1 },
  { name: '推荐信息', value: 2 }
]);

const noticeList = ref(['11请根据车辆运输能力合理选择货源', '22请根据车辆运输能力合理选择货源承运，避免超载超限。', '33请根据车辆运输能力合理选择货源承运，避免超载超限。', '44请根据车辆运输能力合理选择货源承运，避免超载超限。'])
const showServiceFee = ref(false)

const showServiceFeePopup = () => {
  console.log('点击了技术服务费说明')
  showServiceFee.value = true
}

// 老板信用评分
const bossCredit = ref(3.5)

// 推荐货源列表
const recommendList = ref([
])

// 价格详情弹窗控制
const showPriceDetail = ref(false);

// 移除页面样式控制
const pageStyle = computed(() => {
  return '';
});

// 移除watch逻辑
watch(showPriceDetail, (newVal) => {
  // 移除所有锁定滚动的逻辑
});

// 修改关闭弹窗逻辑
const closePriceDetail = () => {
  showPriceDetail.value = false;
};

// 计算底部操作栏样式
const bottomActionStyle = computed(() => {
  // #ifdef APP-PLUS
  return {
    paddingBottom: systemInfo.safeAreaBottom + 'px'
  }
  // #endif

  // #ifdef H5
  return {
    paddingBottom: '20rpx'
  }
  // #endif
});

// 获取底部安全区域高度的计算属性
const safeAreaPadding = computed(() => {
  // #ifdef APP-PLUS
  return systemInfo.safeAreaBottom > 0 ? '20rpx' : '0';
  // #endif

  // #ifdef H5
  return '16rpx';
  // #endif
});

// 页面加载时初始化
onLoad(() => {
  // 移除初始化代码
})

// 监听页面显示事件
onShow(() => {
  // 移除初始化代码
})

// 组件卸载时清理定时器
onUnmounted(() => {

})

onPageScroll((res) => {
  let scrollTop = res.scrollTop;

  // 统一处理透明度
  if (scrollTop <= 20) {
    transparency.value = 1;
    transparency2.value = 0;
  } else if (scrollTop > 20 && scrollTop < 80) {
    transparency.value = 1 - scrollTop / 80;
    transparency2.value = scrollTop / 80;
  } else {
    transparency.value = 0;
    transparency2.value = 1;
  }

  // #ifdef H5
  const routeInfo = document.querySelector('#routeInfoBox')
  const shipmentInfo = document.querySelector('#shipmentInfoBox')
  const recommend = document.querySelector('#recommendBox')

  if (routeInfo && shipmentInfo && recommend) {
    const routeInfoRect = routeInfo.getBoundingClientRect()
    const shipmentInfoRect = shipmentInfo.getBoundingClientRect()
    const recommendRect = recommend.getBoundingClientRect()

    const viewportHeight = window.innerHeight
    const bufferZone = viewportHeight * 0.3

    if (routeInfoRect.top > -bufferZone && routeInfoRect.top < bufferZone) {
      current.value = 0
    } else if (shipmentInfoRect.top > -bufferZone && shipmentInfoRect.top < bufferZone) {
      current.value = 1
    } else if (recommendRect.top > -bufferZone && recommendRect.top < bufferZone) {
      current.value = 2
    }
  }
  // #endif

  // #ifdef APP-PLUS
  const query = uni.createSelectorQuery()
  query.select('#routeInfoBox').boundingClientRect()
  query.select('#shipmentInfoBox').boundingClientRect()
  query.select('#recommendBox').boundingClientRect()
  query.selectViewport().boundingClientRect()
  query.exec((res) => {
    if (res && res[0] && res[1] && res[2] && res[3]) {
      const routeInfo = res[0]
      const shipmentInfo = res[1]
      const recommend = res[2]
      const viewport = res[3]

      const routeInfoTop = routeInfo.top - viewport.top
      const shipmentInfoTop = shipmentInfo.top - viewport.top
      const recommendTop = recommend.top - viewport.top

      const viewportHeight = viewport.height
      const bufferZone = viewportHeight * 0.3

      if (routeInfoTop > -bufferZone && routeInfoTop < bufferZone) {
        current.value = 0
      } else if (shipmentInfoTop > -bufferZone && shipmentInfoTop < bufferZone) {
        current.value = 1
      } else if (recommendTop > -bufferZone && recommendTop < bufferZone) {
        current.value = 2
      }
    }
  })
  // #endif
})

// 抢单
const driverGrabOrderInfo = () => {
	driverGrabOrders({
		id: receivedId.value
	}).then((response: any) => {
		if (response.code == 700) {
			uni.showToast({
			  title: '抢单成功',
			  icon: 'success',
			  duration: 2000
			});
			uni.navigateTo({
				url: "/pageComDriver/pay/pay?orderId=" + receivedId.value + "&money=" + 0.1
			});
		} else {
			uni.showToast({
			  title: response.msg,
			  icon: 'none',
				duration: 2000
			});
		}
	})
}

// 去装货地导航
const goToLoadingLocation = () => {
  if (!orderDetail.value || !orderDetail.value.loadingLongitude || !orderDetail.value.loadingLatitude) {
    uni.showToast({
      title: '暂无装货地定位信息',
      icon: 'none',
      duration: 2000
    });
    return;
  }
  
  // 跳转到local_goods页面并传递参数（只传递终点参数）
  uni.navigateTo({
    url: `/pageComDriver/common/local_goods?end_latitude=${orderDetail.value.loadingLatitude}&end_longitude=${orderDetail.value.loadingLongitude}&end_name=${encodeURIComponent(orderDetail.value.loadingAddressName || '')}`
  });
}

// 查看路线（从装货地到卸货地）
const viewRouteInfo = () => {
  if (!orderDetail.value || 
      !orderDetail.value.loadingLongitude || !orderDetail.value.loadingLatitude || 
      !orderDetail.value.unloadingLongitude || !orderDetail.value.unloadingLatitude) {
    uni.showToast({
      title: '暂无完整的装卸货地定位信息',
      icon: 'none',
      duration: 2000
    });
    return;
  }
  
  // 跳转到local_goods页面并传递装货地和卸货地参数
  uni.navigateTo({
    url: `/pageComDriver/common/local_goods?start_latitude=${orderDetail.value.loadingLatitude}&start_longitude=${orderDetail.value.loadingLongitude}&start_name=${encodeURIComponent(orderDetail.value.loadingAddressName || '')}&end_latitude=${orderDetail.value.unloadingLatitude}&end_longitude=${orderDetail.value.unloadingLongitude}&end_name=${encodeURIComponent(orderDetail.value.unloadingAddressName || '')}`
  });
};
</script>

<style lang="scss" scoped>
.items {
  display: flex;

  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    text {
      color: #000;
      font-size: $uni-font-size-sm;
    }
  }
}

.container-bg {
  position: fixed;
  top: v-bind(heightHeader);
  left: 0rpx;
  width: 100%;
  height: 750rpx;
  background: v-bind(containerBg);
  z-index: 2;
}

.content-container {
  position: relative;
  top: v-bind(heightHeader);
  z-index: 3;
  box-sizing: border-box;
  /* #ifdef H5 */
  height: auto;
  overflow: visible;
  /* #endif */
}

.container-tab {
  position: fixed;
  left: 0;
  top: v-bind(heightHeader);
  width: 100%;
  z-index: 2;
  background-color: #f2f2f2;
  transition: opacity 0.3s ease;
  /* #ifdef H5 */
  will-change: opacity;
  /* #endif */
}

.custom-tabs {
  display: flex;
  background-color: #f2f2f2;
  height: 80rpx;
  position: relative;

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;

    .tab-text {
      font-size: 26rpx;
      color: #333;
      transition: all 0.3s;
    }

    .tab-line {
      position: absolute;
      bottom: 10rpx;
      width: 50rpx;
      height: 4rpx;
      background-color: #e84138;
      border-radius: 2rpx;
      transition: all 0.3s;
    }

    &.active {
      .tab-text {
        font-weight: bold;
        color: #333;
      }
    }
  }
}

.goods-type-box {
  padding: 0rpx 20rpx;
  box-sizing: border-box;

  .goods-type {
    padding: 16rpx 20rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 10rpx;

    text {
      padding: 0rpx 10rpx;
      background-color: #7ac251;
      color: #fff;
      font-size: 24rpx;
      border-radius: 6rpx;
    }

    .goods-type-line {
      width: 2rpx;
      height: 24rpx;
      background-color: #dadada;
      margin: 0rpx 20rpx;
    }

    .goods-type-tips {
      font-size: 26rpx;
      color: #333;
    }
  }
}

.route-info-box {
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-top: 20rpx;

  .route-info {
    background-color: #fff;
    padding: 0rpx 30rpx 30rpx 30rpx;
    box-sizing: border-box;
    border-radius: 10rpx;

    .route-info-base {
      display: flex;
      align-items: center;
      padding: 20rpx 0rpx;
      border-bottom: 1rpx solid #f1f1f1;

      text {
        font-size: 28rpx;
        color: #333;
        font-weight: 700;
        margin-left: 20rpx;
      }
    }

    .address-box {
      margin-top: 20rpx;

      .loading-info-line {
        position: relative;

        &:before {
          position: absolute;
          left: 20rpx;
          top: 40rpx;
          content: '';
          width: 2rpx;
          height: 100%;
          border-left: 2rpx dashed $uni-text-color-grey;
        }
      }

      .loading-info {
        padding: 0rpx 0rpx;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;

        .loading-info-left {
          display: flex;
          align-items: flex-start;
          flex: 1;

          .icon-text {
            width: 40rpx;
            height: 40rpx;
            text-align: center;
            line-height: 40rpx;
            color: #fff;
            background-color: blue;
            font-size: $uni-font-size-base;
            border-radius: 10rpx;
            margin-right: 14rpx;
          }

          .loading-info-left-address {
            flex: 1;

            .big-name {
              font-size: $uni-font-size-base-30;
              color: $uni-text-color;
            }

            .small-name {
              font-size: $uni-font-size-base;
              color: $uni-text-color-grey;
              margin-top: 8rpx;
            }
          }
        }
      }

      .unloading-info {
        margin-top: 40rpx;

        .loading-info-left {
          .icon-text {
            background-color: #ee7942;
          }
        }
      }
    }

    .car-info {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 0rpx;
      box-sizing: border-box;

      .car-info-left {
        display: flex;
        align-items: center;

        text {
          color: #999;
          font-size: 24rpx;
          margin-left: 10rpx;
        }
      }
    }

    .distance-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #fafafa;
      padding: 30rpx 0rpx;
      border-radius: 10rpx;
	  margin-top: 25rpx;

      .distance-info-left {
        width: 50%;
        display: flex;
        align-items: center;
        padding: 0rpx 20rpx;

        .distance-info-left-type1 {
          flex: 1;
          display: flex;
          flex-direction: column;

          .num-unit {
            .num {
              font-size: 32rpx;
              font-weight: bold;
            }

            .unit {
              font-size: 22rpx;
              font-weight: bold;
            }
          }

          .label {
            font-size: 22rpx;
            color: #999;
          }
        }

        .distance-info-left-type2 {
          width: 120rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .iconfont {
            font-size: 38rpx;
            color: #4a98f0;
          }

          .icon-owner--_xianlu {
            font-size: 40rpx;
            font-weight: bold;
          }

          text {
            font-size: 22rpx;
            color: #4a98f0;
          }
        }
      }

      .distance-info-line {
        width: 2rpx;
        height: 50rpx;
        background-color: #e9e9e9;
      }
    }
  }
}

.shipment-info-box {
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-top: 20rpx;

  .shipment-info {
    background-color: #fff;
    padding: 30rpx 30rpx 30rpx 30rpx;
    box-sizing: border-box;
    border-radius: 10rpx;

    .shipment-info-title {
      font-size: 28rpx;
      font-weight: 700;
      margin-bottom: 30rpx;
    }

    .shipment-info-waring {
      display: flex;
      align-items: flex-start;
      margin-top: 20rpx;

      text {
        font-size: 26rpx;
        color: #e64339;
        margin-top: -4rpx;
        margin-left: 5rpx;
      }
    }

    .notice-box {
      :deep(.u-notice-bar) {
        .u-notice {
          .u-notice__swiper {
            height: 64rpx;
          }
        }
      }
    }
  }
}

.price-info-box {
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-top: 20rpx;

  .price-info {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 10rpx;

    .price-header {
      .price-type {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        text {
          color: #e84138;
          font-size: 28rpx;
          margin-left: 10rpx;
        }
      }

      .price-desc {
        font-size: 26rpx;
        color: #666;
      }
    }

    .price-content {
      margin-top: 20rpx;

      .price-main {
        text-align: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        .price-label {
          font-size: 28rpx;
          color: #333;
        }

        .price-value {
          margin: 10rpx 0;

          .price-number {
            font-size: 48rpx;
            font-weight: bold;
            color: #e84138;
          }

          .price-unit {
            font-size: 28rpx;
            color: #e84138;
            margin-left: 4rpx;
          }
        }

        .price-calc {
          font-size: 24rpx;
          color: #999;
        }
      }

      .price-details {
        padding: 20rpx 0;

        .price-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16rpx;

          .item-label {
            font-size: 26rpx;
            color: #666;
            display: flex;
            align-items: center;

            .refund-tag {
              color: #999;
              font-size: 24rpx;
              margin-left: 4rpx;
            }
          }

          .item-value {
            font-size: 26rpx;
            color: #333;

            .original-price {
              color: #999;
              text-decoration: line-through;
              margin-right: 10rpx;
            }

            .current-price {
              color: #333;
            }
          }

          &.discount {
            .item-value {
              color: #e84138;
            }
          }
        }
      }

      .price-action {
        margin-top: 30rpx;

        :deep(.u-button) {
          width: 100%;
          height: 80rpx;
          background: linear-gradient(to right, #e84138, #f0483f);
          border: none;
        }
      }
    }
  }
}

.price-details {
  .price-item {
    .item-label-box {
      display: flex;
      align-items: center;
      gap: 4rpx;
      font-size: 26rpx;
      color: #666;
    }
  }
}

.service-fee-popup {
  width: 600rpx;
  padding: 40rpx;
  box-sizing: border-box;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .popup-content {
    .content-item {
      margin-bottom: 30rpx;

      .item-title {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        text {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-left: 10rpx;
        }
      }

      .item-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        padding-left: 46rpx;
      }
    }
  }

  .popup-footer {
    margin-top: 40rpx;

    :deep(.u-button) {
      width: 100%;
      height: 80rpx;
      background: linear-gradient(to right, #e84138, #f0483f);
      border: none;
    }
  }
}

.boss-info-box {
  padding: 0 20rpx;
  margin-top: 20rpx;

  .boss-info {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 10rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .boss-left {
      display: flex;
      align-items: flex-start;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .info {
        .name-wrap {
          display: flex;
          align-items: center;
          margin-bottom: 10rpx;
          flex-wrap: nowrap;

          .name {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            margin-right: 20rpx;
            white-space: nowrap;
          }

          .credit {
            display: flex;
            align-items: center;
            white-space: nowrap;

            .credit-text {
              font-size: 24rpx;
              color: #666;
              margin-right: 6rpx;
              white-space: nowrap;
            }
          }
        }

        .stats {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 10rpx;

          .divider {
            margin: 0 10rpx;
            color: #ddd;
          }
        }

        .certifications {
          display: flex;
          gap: 20rpx;

          .cert-item {
            display: flex;
            align-items: center;

            text {
              font-size: 24rpx;
              color: #7ac251;
              margin-left: 4rpx;
            }
          }
        }
      }
    }

    .follow-btn {
      :deep(.u-button) {
        padding: 0 30rpx;
      }
    }
  }
}

.recommend-box {
  padding: 0 20rpx;
  margin-top: 20rpx;

  .recommend-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    padding: 0 10rpx;
  }

  .recommend-list {
    .recommend-item {
      background-color: #fff;
      padding: 30rpx;
      border-radius: 10rpx;
      margin-bottom: 20rpx;

      .route {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .cities {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;

          .arrow {
            margin: 0 20rpx;
            color: #999;
          }
        }

        .tag {
          padding: 4rpx 16rpx;
          border-radius: 6rpx;
          font-size: 24rpx;

          &.carpool {
            background-color: #e8f5e9;
            color: #7ac251;
          }

          &.fixed-price {
            background-color: #fff2f0;
            color: #e84138;
          }
        }
      }

      .goods-info {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;

        .time {
          margin-left: 20rpx;
        }
      }

      .specs {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 20rpx;

        .divider {
          margin: 0 16rpx;
          color: #ddd;
        }
      }

      .user-info {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-top: 1rpx solid #f5f5f5;
        border-bottom: 1rpx solid #f5f5f5;

        .avatar {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          margin-right: 10rpx;
        }

        .credit,
        .deals,
        .rate {
          font-size: 24rpx;
          color: #666;
          margin-right: 20rpx;
        }
      }

      .price-action {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;

        .price {
          .amount {
            font-size: 36rpx;
            font-weight: bold;
            color: #e84138;
          }

          .unit {
            font-size: 24rpx;
            color: #e84138;
            margin-left: 4rpx;
          }
        }

        .auth-tip {
          font-size: 24rpx;
          color: #999;
          flex: 1;
          margin: 0 20rpx;
        }

        :deep(.u-button) {
          background: linear-gradient(to right, #e84138, #f0483f);
          border: none;
          flex-shrink: 0;

          &::after {
            border: none;
          }
        }
      }
    }
  }
}

.page-container {
  position: relative;
  min-height: 100vh;
  /* #ifdef H5 */
  height: auto;
  overflow: visible;
  /* #endif */
}

.bottom-placeholder {
  /* #ifdef APP-PLUS */
  height: calc(120rpx + env(safe-area-inset-bottom));
  /* #endif */

  /* #ifdef H5 */
  height: 130rpx;
  /* #endif */
  width: 100%;
}

.bottom-action-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .bottom-action {
    position: relative;
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;

    /* #ifdef APP-PLUS */
    padding: 16rpx 30rpx calc(16rpx + env(safe-area-inset-bottom));
    /* #endif */

    /* #ifdef H5 */
    padding: 20rpx;
    /* #endif */

    .price-info {
      display: flex;
      align-items: baseline;
      flex: 1;
      margin-right: 24rpx;

      .amount {
        font-size: 40rpx;
        font-weight: bold;
        color: #e84138;
        line-height: 1;
      }

      .unit {
        font-size: 26rpx;
        color: #e84138;
        margin-right: 12rpx;
        line-height: 1;
      }

      .desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1;
      }

      .detail {
        font-size: 24rpx;
        color: #999;
        margin-left: 24rpx;
        position: relative;
        padding-right: 24rpx;
        cursor: pointer;

        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%) rotate(45deg);
          width: 12rpx;
          height: 12rpx;
          border: 2rpx solid #999;
          border-left: 0;
          border-bottom: 0;
        }
      }
    }

    .buttons {
      display: flex;
      gap: 24rpx;

      :deep(.u-button) {
        /* #ifdef APP-PLUS */
        min-width: 140rpx;
        height: 72rpx;
        /* #endif */
        /* #ifdef H5 */
        min-width: 150rpx;
        height: 76rpx;
        /* #endif */
        font-size: 28rpx;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40rpx;
      }

      :deep(.u-button--primary) {
        background: linear-gradient(to right, #e84138, #f0483f);
        border: none;
        box-shadow: 0 4rpx 8rpx rgba(232, 65, 56, 0.2);
      }

      :deep(.u-button--info) {
        border-color: #e84138;
        color: #e84138;

        &::after {
          border-color: #e84138;
        }
      }
    }
  }
}

// 处理 iOS 安全区域
.safe-area-bottom {
  width: 100%;
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
  background-color: #fff;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  z-index: 9998;
  pointer-events: auto;
}

.price-detail-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(0);
  transition: transform 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;

  /* #ifdef APP-PLUS */
  padding-bottom: v-bind(safeAreaPadding);
  /* #endif */

  /* #ifdef H5 */
  padding-bottom: v-bind(safeAreaPadding);
  /* #endif */

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .popup-content {
    .price-item {
      margin-bottom: 30rpx;

      &.main-price {
        padding-bottom: 30rpx;
        border-bottom: 1rpx solid #f5f5f5;
        margin-bottom: 30rpx;

        .calc {
          font-size: 24rpx;
          color: #999;
          margin-top: 8rpx;
        }
      }

      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;

        .tag {
          font-size: 24rpx;
          color: #999;
        }
      }

      .label-box {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        font-size: 28rpx;
        color: #333;

        .discount-tag {
          margin-left: 12rpx;
          padding: 2rpx 8rpx;
          font-size: 22rpx;
          color: #e84138;
          background: rgba(232, 65, 56, 0.1);
          border-radius: 4rpx;
        }
      }

      .value {
        display: flex;
        align-items: baseline;

        .amount {
          font-size: 36rpx;
          font-weight: bold;
          color: #333;
        }

        .unit {
          font-size: 24rpx;
          color: #333;
          margin-left: 4rpx;
        }

        .original {
          font-size: 24rpx;
          color: #999;
          text-decoration: line-through;
          margin-right: 12rpx;
        }

        .discount {
          font-size: 24rpx;
          color: #e84138;
          margin-left: 12rpx;
        }
      }
    }

    .tips {
      font-size: 24rpx;
      color: #999;
      text-align: center;
      margin-top: 40rpx;
      margin-bottom: 20rpx;
    }
  }
}

// 添加弹窗遮罩层样式
:deep(.up-popup__mask) {
  backdrop-filter: blur(3px);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
// 加价是否同意弹窗
:deep(.price-add-popup) {
  .uni-popup__wrapper {
    width: 100%;
    position: absolute;
    bottom: 0;
    // bottom: v-bind(tabbarHpx);
    .popup-content {
      height: 500rpx;
      background-color: #fff;
      .confirm-btn {
        width: 90%;
        margin-left: 5%;
        position: absolute;
        bottom: 20rpx;
        .btn-com {
          display: inline-block;
          width: 40%;
          margin: 0 5%;
          height: 60rpx;
          line-height: 60rpx;
          text-align: center;
          background-color: red;
          color: #fff;
          border-radius: 5rpx;
        }
      }
      .count-amount-box {
        width: 100%;
        padding: 0rpx 30rpx;
        box-sizing: border-box;
        margin-top: 20rpx;
        .count-amount {
          width: 100%;
          height: 90rpx;
          margin-bottom: 20rpx;
          background-color: $uni-bg-color-grey;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-radius: 10rpx;
          padding-right: 20rpx;
          box-sizing: border-box;
          .price {
            padding-left: 10rpx;
            width: 60rpx;
          }
          .price-input {
            // flex: none;
            .u-input__content {
              padding-left: 20rpx;
              box-sizing: border-box;
              width: 300rpx;
              flex: none;
              .u-input__content__field-wrapper {
                .u-input__content__field-wrapper__field {
                  .uni-input-wrapper {
                    .uni-input-input {
                      font-size: $uni-font-size-big;
                      color: #333;
                      font-weight: bold;
                    }
                  }
                }
              }
            }
          }
          .count-amount-right {
            display: flex;
            align-items: center;
            .text {
              font-size: $uni-font-size-base;
              color: #333;
            }
            .unit-list {
              display: flex;
              align-items: center;
              margin-left: 20rpx;
              .unit-item {
                width: 90rpx;
                height: 60rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                border: 1px solid #fff;
                font-size: $uni-font-size-base;
                background-color: #fff;
                border-radius: 10rpx;
                margin-right: 10rpx;
                color: #333;
                &:last-child {
                  margin-right: 0rpx;
                }
              }
              .unit-item-active {
                color: #ff8c00;
                border-color: #ff8c00;
              }
            }
          }
        }
        .total-freight {
          font-size: $uni-font-size-base-30;
          color: #ff8c00;
          margin-top: 20rpx;
        }
      }
    }
  }
}
</style>
